{"name": "wow-raid-reminder-bot", "version": "1.0.0", "description": "Telegram бот для мониторинга рейдов WoW с уведомлениями о заполненности", "main": "index.js", "scripts": {"start": "node index.js", "start:bot": "cross-env MODE=bot node index.js", "start:web": "cross-env MODE=web node index.js", "start:both": "cross-env MODE=both node index.js", "dev": "nodemon index.js", "dev:bot": "cross-env MODE=bot nodemon index.js", "dev:web": "cross-env MODE=web nodemon index.js", "dev:both": "cross-env MODE=both nodemon index.js", "init-config": "node scripts/init-config-sheet.js", "check-env": "node scripts/check-env-vars.js", "test": "jest"}, "keywords": ["telegram", "bot", "wow", "raid", "monitoring", "google-sheets"], "author": "WoW Raid Team", "license": "MIT", "dependencies": {"dotenv": "^16.6.1", "express": "^4.18.2", "express-session": "^1.17.3", "googleapis": "^144.0.0", "moment-timezone": "^0.6.0", "node-cron": "^3.0.3", "session-file-store": "^1.5.0", "telegraf": "^4.16.3", "vercel": "^44.6.4"}, "devDependencies": {"cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.1.10"}, "engines": {"node": ">=18.0.0"}}