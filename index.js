#!/usr/bin/env node

/**
 * WoW Raid Reminder Bot
 * Telegram бот для мониторинга коммерческих рейдов World of Warcraft
 */

const WoWRaidBot = require('./src/bot/bot');
const ConfigWebServer = require('./src/web/server');
const APIServer = require('./src/api/apiServer');

async function main() {
  const mode = process.env.MODE || 'bot'; // 'bot', 'web', или 'both'

  console.log(`🚀 Запуск WoW Raid Reminder Bot в режиме: ${mode}`);

  let bot = null;
  let webServer = null;
  let apiServer = null;

  try {
    if (mode === 'web' || mode === 'both') {
      // Запуск веб-сервера
      webServer = new ConfigWebServer();
      webServer.initialize();
      await webServer.start();
    }

    if (mode === 'bot' || mode === 'both') {
      // Запуск бота
      bot = new WoWRaidBot();
      await bot.initialize();
      await bot.start();
      console.log('✅ Бот успешно запущен и работает');

      // Запуск API сервера
      apiServer = new APIServer();
      apiServer.setBotInstance(bot);
      apiServer.initialize();
      await apiServer.start();
      console.log('✅ API сервер успешно запущен');

      // Связываем веб-сервер с ботом если оба запущены
      if (webServer) {
        webServer.setBotInstance(bot);
      }
    }

    if (mode === 'web') {
      console.log('✅ Веб-сервер конфигурации запущен');
    }

  } catch (error) {
    console.error('❌ Критическая ошибка:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Запуск приложения
if (require.main === module) {
  main().catch(error => {
    console.error('Необработанная ошибка:', error);
    process.exit(1);
  });
}

module.exports = { WoWRaidBot };