const fs = require('fs');
const path = require('path');

/**
 * Класс для хранения игнорируемых заказов в JSON файле
 */
class IgnoredOrdersStore {
  constructor() {
    this.filePath = path.join(__dirname, '..', 'data', 'ignoredOrders.json');
    this.ignoredOrderIds = new Set();
  }

  /**
   * Инициализация - загрузка данных из файла
   */
  async initialize() {
    try {
      // Убеждаемся, что директория существует
      const dir = path.dirname(this.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Загружаем данные из файла, если он существует
      if (fs.existsSync(this.filePath)) {
        const data = fs.readFileSync(this.filePath, 'utf8');
        const parsedData = JSON.parse(data);
        
        // Восстанавливаем Set из массива
        if (Array.isArray(parsedData.ignoredOrderIds)) {
          this.ignoredOrderIds = new Set(parsedData.ignoredOrderIds);
        }
        
        console.log(`Загружено ${this.ignoredOrderIds.size} игнорируемых заказов`);
      } else {
        // Создаем пустой файл
        this.save();
        console.log('Создан новый файл для игнорируемых заказов');
      }
    } catch (error) {
      console.error('Ошибка при инициализации IgnoredOrdersStore:', error.message);
      // В случае ошибки создаем пустой Set
      this.ignoredOrderIds = new Set();
    }
  }

  /**
   * Сохранение данных в файл
   */
  save() {
    try {
      const data = {
        ignoredOrderIds: Array.from(this.ignoredOrderIds),
        lastUpdated: new Date().toISOString()
      };
      
      fs.writeFileSync(this.filePath, JSON.stringify(data, null, 2), 'utf8');
    } catch (error) {
      console.error('Ошибка при сохранении игнорируемых заказов:', error.message);
    }
  }

  /**
   * Проверка, игнорируется ли заказ
   */
  has(orderId) {
    return this.ignoredOrderIds.has(orderId);
  }

  /**
   * Добавление заказа в игнор-лист
   */
  add(orderId) {
    this.ignoredOrderIds.add(orderId);
    this.save();
  }

  /**
   * Удаление заказа из игнор-листа
   */
  remove(orderId) {
    const removed = this.ignoredOrderIds.delete(orderId);
    if (removed) {
      this.save();
    }
    return removed;
  }

  /**
   * Получение всех игнорируемых заказов
   */
  getAll() {
    return Array.from(this.ignoredOrderIds);
  }

  /**
   * Очистка всех игнорируемых заказов
   */
  clear() {
    this.ignoredOrderIds.clear();
    this.save();
  }

  /**
   * Получение количества игнорируемых заказов
   */
  size() {
    return this.ignoredOrderIds.size;
  }
}

module.exports = IgnoredOrdersStore;
