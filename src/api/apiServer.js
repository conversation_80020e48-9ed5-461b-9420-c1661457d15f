const express = require('express');
const cors = require('cors');

/**
 * API сервер для внешних запросов
 */
class APIServer {
  constructor() {
    this.app = express();
    this.port = 61690;
    this.authToken = process.env.AUTH_TOKEN;
    this.botInstance = null;
  }

  /**
   * Инициализация API сервера
   */
  initialize() {
    // Настройка middleware
    this.app.use(cors());
    this.app.use(express.json());

    // Middleware для логирования
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });

    this.setupRoutes();
  }

  /**
   * Настройка маршрутов
   */
  setupRoutes() {
    // Middleware для проверки авторизации
    const requireAuth = (req, res, next) => {
      const authToken = req.query.authorization || req.headers.authorization;
      
      if (!authToken || authToken !== this.authToken) {
        return res.status(401).json({
          success: false,
          error: 'Unauthorized',
          message: 'Неверный токен авторизации'
        });
      }
      next();
    };

    // API эндпоинт для получения информации о командах (аналог команды /teams)
    this.app.get('/teams', requireAuth, async (req, res) => {
      try {
        // Проверяем доступность сервиса мониторинга
        if (!this.botInstance || !this.botInstance.raidMonitor) {
          return res.status(503).json({
            success: false,
            error: 'Service Unavailable',
            message: 'Сервис мониторинга рейдов недоступен'
          });
        }

        // Получаем данные команд
        const teamsData = await this.getTeamsDataForAPI();
        
        res.json({
          success: true,
          data: teamsData
        });

      } catch (error) {
        console.error('Ошибка API /teams:', error.message);
        res.status(500).json({
          success: false,
          error: 'Internal Server Error',
          message: 'Внутренняя ошибка сервера'
        });
      }
    });

    // Базовый эндпоинт для проверки работоспособности
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString()
      });
    });

    // 404 для всех остальных маршрутов
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Эндпоинт не найден'
      });
    });
  }

  /**
   * Получение данных команд для API (аналог команды /teams)
   */
  async getTeamsDataForAPI() {
    try {
      if (!this.botInstance || !this.botInstance.raidMonitor) {
        throw new Error('Сервис мониторинга рейдов недоступен');
      }

      const raidMonitor = this.botInstance.raidMonitor;

      if (!raidMonitor.initialized) {
        throw new Error('Сервис мониторинга не инициализирован');
      }

      console.log('Получение данных команд для API...');

      // Получаем данные
      const [clients, schedule] = await Promise.all([
        raidMonitor.sheetsService.getClientsData(),
        raidMonitor.scheduleService.getCombinedScheduleData(),
      ]);

      const internalSchedule = await raidMonitor.internalScheduleService.getInternalScheduleData();

      // Фильтруем рейды начиная с сегодняшнего дня
      const DateUtils = require('../utils/dateUtils');
      const relevantRaids = schedule.filter(raid => {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) return false;
        return DateUtils.isDateTodayOrFuture(raidDateTime);
      });

      if (relevantRaids.length === 0) {
        return {
          summary: {
            totalRaids: 0,
            openRaids: 0,
            closedRaids: 0,
            raidsWithIssues: 0,
            totalEmptyTeams: 0,
            totalWrongTeams: 0
          },
          raids: [],
          timestamp: new Date().toISOString()
        };
      }

      console.log(`Проверяем команды для ${relevantRaids.length} рейдов`);

      const raidStats = [];

      // Проверяем каждый рейд на проблемы с командами
      for (const raid of relevantRaids) {
        // Получаем клиентов для этого рейда
        const RaidUtils = require('../utils/raidUtils');
        const raidClients = clients.filter(client =>
          client.date === raid.date &&
          client.time === raid.time &&
          RaidUtils.isRaidTypeAllowed(raid.raidType, client.raidType)
        );

        // Получаем информацию о командах
        const teamInfo = await raidMonitor.getTeamInfoForRaid(
          raid.date, 
          raid.time, 
          raid.raidType, 
          internalSchedule, 
          raid
        );

        const raidStat = {
          date: raid.date,
          time: raid.time,
          raidType: raid.raidType,
          status: raid.status,
          clientCount: raidClients.length,
          expectedTeams: teamInfo.teams || [],
          totalLimit: teamInfo.totalLimit || 0,
          emptyTeams: 0,
          wrongTeams: 0,
          issues: []
        };

        if (raidClients.length > 0) {
          // Проверяем пустые команды
          const emptyTeamIssues = RaidUtils.checkTeamAssignments(raid, raidClients);
          raidStat.emptyTeams = emptyTeamIssues.length;

          if (emptyTeamIssues.length > 0) {
            raidStat.issues.push(...emptyTeamIssues.map(issue => ({
              type: 'empty_team',
              message: issue
            })));
          }

          // Проверяем правильность команд
          const teamMismatches = await raidMonitor.checkTeamMismatches(raidClients, internalSchedule, raid);
          raidStat.wrongTeams = teamMismatches.length;

          if (teamMismatches.length > 0) {
            teamMismatches.forEach(mismatch => {
              const orderInfo = mismatch.orderId ? `Заказ: "${mismatch.orderId}" ` : '';
              raidStat.issues.push({
                type: 'wrong_team',
                rowNumber: mismatch.rowNumber,
                orderId: mismatch.orderId,
                clientTeam: mismatch.clientTeam,
                message: `Строка ${mismatch.rowNumber}: ${orderInfo}команда "${mismatch.clientTeam}" не соответствует расписанию`
              });
            });
          }
        }

        raidStats.push(raidStat);
      }

      // Подсчитываем общую статистику
      const totalRaids = raidStats.length;
      const openRaids = raidStats.filter(stat => stat.status !== 'закрыт').length;
      const closedRaids = raidStats.filter(stat => stat.status === 'закрыт').length;
      const raidsWithIssues = raidStats.filter(stat => stat.issues.length > 0).length;
      const totalEmptyTeams = raidStats.reduce((sum, stat) => sum + stat.emptyTeams, 0);
      const totalWrongTeams = raidStats.reduce((sum, stat) => sum + stat.wrongTeams, 0);

      return {
        summary: {
          totalRaids,
          openRaids,
          closedRaids,
          raidsWithIssues,
          totalEmptyTeams,
          totalWrongTeams
        },
        raids: raidStats,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Ошибка получения данных команд для API:', error.message);
      throw error;
    }
  }

  /**
   * Установка экземпляра бота для доступа к raidMonitor
   */
  setBotInstance(botInstance) {
    this.botInstance = botInstance;
  }

  /**
   * Запуск сервера
   */
  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`API сервер запущен на порту ${this.port}`);
        console.log(`API доступно по адресу: http://localhost:${this.port}/teams?authorization=${this.authToken}`);
        resolve();
      });
    });
  }

  /**
   * Остановка сервера
   */
  stop() {
    if (this.server) {
      this.server.close();
      console.log('API сервер остановлен');
    }
  }
}

module.exports = APIServer;
